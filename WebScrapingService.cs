using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using System.Collections.Concurrent;

namespace SeleniumWebScraper
{
    /// <summary>
    /// 网页抓取结果类
    /// </summary>
    public class WebScrapingResult
    {
        public string Url { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public bool IsSuccess { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public TimeSpan Duration { get; set; }
    }

    /// <summary>
    /// 基于Selenium的多线程网页抓取服务
    /// </summary>
    public class WebScrapingService : IDisposable
    {
        private readonly int _maxConcurrency;
        private readonly TimeSpan _timeout;
        private readonly SemaphoreSlim _semaphore;
        private bool _disposed = false;

        /// <summary>
        /// 初始化网页抓取服务
        /// </summary>
        /// <param name="maxConcurrency">最大并发数，默认为5</param>
        /// <param name="timeoutSeconds">超时时间（秒），默认为30秒</param>
        public WebScrapingService(int maxConcurrency = 5, int timeoutSeconds = 30)
        {
            _maxConcurrency = maxConcurrency;
            _timeout = TimeSpan.FromSeconds(timeoutSeconds);
            _semaphore = new SemaphoreSlim(maxConcurrency, maxConcurrency);
        }

        /// <summary>
        /// 同时访问多个网址并获取网页内容
        /// </summary>
        /// <param name="urls">要访问的网址列表</param>
        /// <returns>抓取结果列表</returns>
        public async Task<List<WebScrapingResult>> ScrapeWebsitesAsync(IEnumerable<string> urls)
        {
            if (urls == null)
                throw new ArgumentNullException(nameof(urls));

            var urlList = urls.ToList();
            if (!urlList.Any())
                return new List<WebScrapingResult>();

            var results = new ConcurrentBag<WebScrapingResult>();
            var tasks = urlList.Select(url => ScrapeWebsiteAsync(url, results));

            await Task.WhenAll(tasks);

            return results.OrderBy(r => urlList.IndexOf(r.Url)).ToList();
        }

        /// <summary>
        /// 抓取单个网站的内容
        /// </summary>
        /// <param name="url">网址</param>
        /// <param name="results">结果集合</param>
        private async Task ScrapeWebsiteAsync(string url, ConcurrentBag<WebScrapingResult> results)
        {
            await _semaphore.WaitAsync();
            
            try
            {
                var result = await Task.Run(() => ScrapeWebsite(url));
                results.Add(result);
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 使用Selenium抓取网站内容
        /// </summary>
        /// <param name="url">网址</param>
        /// <returns>抓取结果</returns>
        private WebScrapingResult ScrapeWebsite(string url)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            var result = new WebScrapingResult { Url = url };

            IWebDriver? driver = null;
            try
            {
                // 配置Chrome选项
                var options = new ChromeOptions();
                options.AddArgument("--headless"); // 无头模式
                options.AddArgument("--no-sandbox");
                options.AddArgument("--disable-dev-shm-usage");
                options.AddArgument("--disable-gpu");
                options.AddArgument("--window-size=1920,1080");
                options.AddArgument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");

                // 创建WebDriver实例
                driver = new ChromeDriver(options);
                driver.Manage().Timeouts().PageLoad = _timeout;
                driver.Manage().Timeouts().ImplicitWait = TimeSpan.FromSeconds(10);

                // 访问网页
                driver.Navigate().GoToUrl(url);

                // 等待页面加载完成
                await Task.Delay(2000);

                // 获取页面内容
                result.Content = driver.PageSource;
                result.IsSuccess = true;
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.ErrorMessage = ex.Message;
                Console.WriteLine($"抓取网址 {url} 时发生错误: {ex.Message}");
            }
            finally
            {
                try
                {
                    driver?.Quit();
                    driver?.Dispose();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"关闭WebDriver时发生错误: {ex.Message}");
                }
                
                stopwatch.Stop();
                result.Duration = stopwatch.Elapsed;
            }

            return result;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                _semaphore?.Dispose();
                _disposed = true;
            }
        }
    }
}
