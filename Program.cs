using SeleniumWebScraper;

namespace SeleniumWebScraper
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("=== Selenium多线程网页抓取工具 ===\n");

            // 示例网址列表
            var urls = new List<string>
            {
                "https://www.baidu.com",
                "https://www.bing.com",
                "https://www.google.com",
                "https://github.com",
                "https://stackoverflow.com"
            };

            Console.WriteLine("准备抓取以下网址:");
            foreach (var url in urls)
            {
                Console.WriteLine($"- {url}");
            }
            Console.WriteLine();

            // 创建网页抓取服务实例
            using var scrapingService = new WebScrapingService(maxConcurrency: 5, timeoutSeconds: 30);

            try
            {
                Console.WriteLine("开始抓取...");
                var startTime = DateTime.Now;

                // 执行多线程抓取
                var results = await scrapingService.ScrapeWebsitesAsync(urls);

                var endTime = DateTime.Now;
                var totalDuration = endTime - startTime;

                Console.WriteLine($"\n抓取完成! 总耗时: {totalDuration.TotalSeconds:F2} 秒\n");

                // 显示结果
                DisplayResults(results);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"发生错误: {ex.Message}");
            }

            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 显示抓取结果
        /// </summary>
        /// <param name="results">抓取结果列表</param>
        private static void DisplayResults(List<WebScrapingResult> results)
        {
            Console.WriteLine("=== 抓取结果 ===");
            
            for (int i = 0; i < results.Count; i++)
            {
                var result = results[i];
                Console.WriteLine($"\n[{i + 1}] {result.Url}");
                Console.WriteLine($"状态: {(result.IsSuccess ? "成功" : "失败")}");
                Console.WriteLine($"耗时: {result.Duration.TotalSeconds:F2} 秒");

                if (result.IsSuccess)
                {
                    var contentLength = result.Content.Length;
                    Console.WriteLine($"内容长度: {contentLength:N0} 字符");
                    
                    // 显示内容预览（前200个字符）
                    var preview = result.Content.Length > 200 
                        ? result.Content.Substring(0, 200) + "..." 
                        : result.Content;
                    
                    Console.WriteLine($"内容预览: {preview.Replace('\n', ' ').Replace('\r', ' ')}");
                }
                else
                {
                    Console.WriteLine($"错误信息: {result.ErrorMessage}");
                }
                
                Console.WriteLine(new string('-', 50));
            }

            // 统计信息
            var successCount = results.Count(r => r.IsSuccess);
            var failureCount = results.Count - successCount;
            var avgDuration = results.Average(r => r.Duration.TotalSeconds);

            Console.WriteLine($"\n=== 统计信息 ===");
            Console.WriteLine($"总数: {results.Count}");
            Console.WriteLine($"成功: {successCount}");
            Console.WriteLine($"失败: {failureCount}");
            Console.WriteLine($"平均耗时: {avgDuration:F2} 秒");
        }
    }
}
