using SeleniumWebScraper;

namespace SeleniumWebScraper
{
    /// <summary>
    /// 简单使用示例
    /// </summary>
    public class SimpleExample
    {
        /// <summary>
        /// 简单的网页抓取函数示例
        /// </summary>
        /// <param name="urls">要访问的网址列表</param>
        /// <returns>网页内容字典，键为URL，值为网页内容</returns>
        public static async Task<Dictionary<string, string>> GetWebContentsAsync(List<string> urls)
        {
            var results = new Dictionary<string, string>();
            
            using var scrapingService = new WebScrapingService(maxConcurrency: 5);
            
            try
            {
                var scrapingResults = await scrapingService.ScrapeWebsitesAsync(urls);
                
                foreach (var result in scrapingResults)
                {
                    if (result.IsSuccess)
                    {
                        results[result.Url] = result.Content;
                    }
                    else
                    {
                        results[result.Url] = $"错误: {result.ErrorMessage}";
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"抓取过程中发生错误: {ex.Message}");
            }
            
            return results;
        }

        /// <summary>
        /// 使用示例
        /// </summary>
        public static async Task RunExample()
        {
            var urls = new List<string>
            {
                "https://www.baidu.com",
                "https://www.bing.com",
                "https://github.com",
                "https://stackoverflow.com",
                "https://www.microsoft.com"
            };

            Console.WriteLine("开始抓取网页内容...");
            
            var webContents = await GetWebContentsAsync(urls);
            
            Console.WriteLine($"抓取完成，共获取 {webContents.Count} 个网页的内容");
            
            foreach (var kvp in webContents)
            {
                Console.WriteLine($"\n网址: {kvp.Key}");
                Console.WriteLine($"内容长度: {kvp.Value.Length} 字符");
                
                // 显示前100个字符作为预览
                var preview = kvp.Value.Length > 100 
                    ? kvp.Value.Substring(0, 100) + "..." 
                    : kvp.Value;
                Console.WriteLine($"预览: {preview.Replace('\n', ' ').Replace('\r', ' ')}");
            }
        }
    }
}
