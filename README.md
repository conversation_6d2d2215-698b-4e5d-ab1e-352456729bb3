# Selenium多线程网页抓取工具

这是一个基于C#和Selenium的多线程网页抓取工具，可以同时访问多个网址并获取网页内容。

## 功能特点

- ✅ 支持同时访问5个网址（可配置并发数）
- ✅ 多线程并发处理，提高效率
- ✅ 无头浏览器模式，节省资源
- ✅ 自动超时处理和错误处理
- ✅ 详细的抓取结果和统计信息
- ✅ 资源自动释放和清理

## 环境要求

- .NET 6.0 或更高版本
- Chrome浏览器（用于ChromeDriver）

## 安装依赖

项目会自动安装以下NuGet包：
- Selenium.WebDriver (4.15.0)
- Selenium.WebDriver.ChromeDriver (119.0.6045.10500)

## 使用方法

### 1. 基本使用

```csharp
using SeleniumWebScraper;

// 创建网址列表
var urls = new List<string>
{
    "https://www.baidu.com",
    "https://www.bing.com",
    "https://github.com",
    "https://stackoverflow.com",
    "https://www.microsoft.com"
};

// 使用抓取服务
using var scrapingService = new WebScrapingService(maxConcurrency: 5);
var results = await scrapingService.ScrapeWebsitesAsync(urls);

// 处理结果
foreach (var result in results)
{
    if (result.IsSuccess)
    {
        Console.WriteLine($"成功抓取 {result.Url}，内容长度: {result.Content.Length}");
    }
    else
    {
        Console.WriteLine($"抓取失败 {result.Url}: {result.ErrorMessage}");
    }
}
```

### 2. 简化使用

```csharp
// 直接获取网页内容字典
var webContents = await SimpleExample.GetWebContentsAsync(urls);

foreach (var kvp in webContents)
{
    Console.WriteLine($"网址: {kvp.Key}");
    Console.WriteLine($"内容: {kvp.Value}");
}
```

## 运行项目

1. 确保已安装Chrome浏览器
2. 在项目目录下运行：

```bash
dotnet restore
dotnet run
```

## 配置选项

### WebScrapingService 构造函数参数

- `maxConcurrency`: 最大并发数（默认5）
- `timeoutSeconds`: 超时时间秒数（默认30秒）

```csharp
// 自定义配置
var service = new WebScrapingService(
    maxConcurrency: 3,    // 最多同时3个线程
    timeoutSeconds: 60    // 60秒超时
);
```

## 返回结果

`WebScrapingResult` 类包含以下属性：

- `Url`: 访问的网址
- `Content`: 网页HTML内容
- `IsSuccess`: 是否成功
- `ErrorMessage`: 错误信息（如果失败）
- `Duration`: 抓取耗时

## 注意事项

1. **Chrome浏览器**: 确保系统已安装Chrome浏览器
2. **网络连接**: 确保网络连接正常
3. **反爬虫**: 某些网站可能有反爬虫机制，可能需要调整User-Agent或添加延迟
4. **资源管理**: 使用`using`语句确保资源正确释放
5. **并发限制**: 不要设置过高的并发数，避免对目标网站造成压力

## 故障排除

### 常见问题

1. **ChromeDriver版本不匹配**
   - 确保ChromeDriver版本与Chrome浏览器版本兼容

2. **网页加载超时**
   - 增加超时时间设置
   - 检查网络连接

3. **内存占用过高**
   - 减少并发数
   - 确保正确释放WebDriver资源

## 扩展功能

可以根据需要扩展以下功能：

- 添加代理支持
- 支持其他浏览器（Firefox、Edge等）
- 添加Cookie和Session管理
- 实现JavaScript执行等待
- 添加重试机制
- 支持文件下载
